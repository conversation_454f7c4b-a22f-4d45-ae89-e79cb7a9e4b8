import React, { ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { removeAuth } from '@/app/services/identity.service';
import { LogoutCurve } from 'iconsax-react';
import { IconText } from '@/app/atoms';

const BrandsLayout = ({ children }: { children: ReactNode }) => {
    const router = useRouter();

    const handleLogout = useCallback((event: React.MouseEvent) => {
        event.preventDefault();
        removeAuth();
        router.push('/signin/pin');
    }, [router]);

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Logout Button - positioned at top right */}
            <div className="absolute top-6 right-6 z-50">
                <button
                    data-automation="logout-brands-page"
                    onClick={handleLogout}
                    className="flex items-center gap-3 px-4 py-2 bg-primary-900 text-white rounded-lg hover:bg-primary-800 transition-colors duration-200 shadow-lg"
                >
                    <IconText
                        variant="bodySmall"
                        fontWeight="font-medium"
                        label="Log Out"
                        icon={<LogoutCurve size={20} />}
                        iconPosition="right"
                        classNameText="text-inherit"
                        className="gap-x-3"
                        textColor="text-inherit"
                    />
                </button>
            </div>

            {/* Main content */}
            <div className="p-6">
                {children}
            </div>
        </div>
    );
};

export default BrandsLayout;
